# Play Astro - Free Astro Template for Startup, SaaS and App Sites
Play Astro is a fantastic free and open-source Astro template, built for startups, SaaS companies, apps, businesses, and more. With its premium design and comprehensive range of essential components and pages, it's your all-in-one solution for launching a full-fledged website.

### Play Astro is crafted using 🥞 [TailGrids](https://tailgrids.com/) UI Components

### [🚀 View Demo](https://play-astro.tailgrids.com/)

[![play-astro](https://github.com/TailGrids/play-astro/blob/main/play-astro.png)](https://play-astro.tailgrids.com/)

### ✍️ Crafted for Startup, SaaS and App Websites
Play Astro is an incredibly versatile and free open-source Astro template. It's been crafted to cater to startups, SaaS, and app websites. This template carries all the essential sections, pages, and components necessary to launch a comprehensive business website. It's perfect for those who want to create a robust online platform without the hassle of starting from scratch.

### ✍️ Awesome Blog with Markdown
Moreover, it features a functional blog system, powered by Markdown, providing everything you need to operate a complete business website.

### ✅ Astro + Tailwind CSS Starter
Crafted using Tailwind CSS with TailGrids components, this high-quality free Astro + Tailwind CSS starter offers unparalleled flexibility. If you're on the lookout for the perfect choice to kickstart your next venture, look no further than Play Astro!

### 🚀 Installation and Deployment Instructions

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`      |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

### 📄 License for Play Astro Template
Play Astro is an open-source template and you are free to utilize it for both personal and commercial projects without the need for any attribution or backlink.

### 💖 How to Support
You can demonstrate your support for this project by giving a Star ⭐ to this repository and sharing it with your friends. If you encounter any bugs, please open an issue. Feel free to contribute to the project through pull requests if you fix an issue or if you believe you can add more value to the project.

## 👀 Want to learn more?

You are welcome to peruse [our documentation](https://tailgrids.com/astro) or join the conversation on our [Discord server](https://pimjo.com/discord).
