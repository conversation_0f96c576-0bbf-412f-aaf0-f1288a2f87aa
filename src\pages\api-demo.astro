---
import Layout from '../layouts/Layout.astro';
import ExternalPosts from '../components/ExternalPosts.astro';

// 页面级别的API调用示例
let weatherData = null;
let newsData = [];

try {
  // 示例1: 获取天气数据 (使用免费API)
  // 注意: 实际使用时需要API密钥
  // const weatherResponse = await fetch('https://api.openweathermap.org/data/2.5/weather?q=Beijing&appid=YOUR_API_KEY');
  // weatherData = await weatherResponse.json();
  
  // 示例2: 获取新闻数据 (模拟)
  const newsResponse = await fetch('https://jsonplaceholder.typicode.com/posts?_limit=5');
  if (newsResponse.ok) {
    const posts = await newsResponse.json();
    newsData = posts.map(post => ({
      id: post.id,
      title: post.title,
      summary: post.body.substring(0, 100) + '...',
      publishTime: new Date().toISOString()
    }));
  }
} catch (error) {
  console.error('API调用失败:', error);
}
---

<Layout 
  title="API调用演示 - Astro接口集成示例"
  description="展示Astro中各种API调用方式的演示页面"
>
  <main>
    <!-- 页面头部 -->
    <section class="relative z-10 overflow-hidden bg-primary pt-[120px] pb-[100px] md:pt-[130px] lg:pt-[160px]">
      <div class="container">
        <div class="-mx-4 flex flex-wrap items-center">
          <div class="w-full px-4">
            <div class="mx-auto max-w-[570px] text-center">
              <h1 class="mb-6 text-3xl font-bold leading-snug text-white sm:text-4xl sm:leading-snug lg:text-5xl lg:leading-[1.2]">
                API调用演示
              </h1>
              <p class="mx-auto mb-9 max-w-[600px] text-base font-medium text-white sm:text-lg sm:leading-[1.44]">
                展示Astro中服务端调用、客户端调用、API路由等多种接口集成方式
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务端API调用示例 -->
    <section class="py-20 bg-white dark:bg-dark">
      <div class="container">
        <div class="mb-12 text-center">
          <h2 class="mb-4 text-3xl font-bold text-dark dark:text-white">
            服务端API调用
          </h2>
          <p class="text-body-color dark:text-dark-6">
            在构建时或服务端渲染时获取数据
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 新闻数据展示 -->
          <div class="rounded-lg bg-gray-1 p-6 dark:bg-dark-2">
            <h3 class="mb-4 text-xl font-semibold text-dark dark:text-white">
              📰 最新资讯 (服务端获取)
            </h3>
            {newsData.length > 0 ? (
              <div class="space-y-4">
                {newsData.map((news) => (
                  <div key={news.id} class="border-b border-gray-200 pb-4 last:border-b-0 dark:border-dark-3">
                    <h4 class="mb-2 font-medium text-dark dark:text-white">
                      {news.title}
                    </h4>
                    <p class="mb-2 text-sm text-body-color dark:text-dark-6">
                      {news.summary}
                    </p>
                    <span class="text-xs text-gray-500">
                      ID: {news.id}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p class="text-body-color dark:text-dark-6">暂无数据</p>
            )}
          </div>

          <!-- API状态展示 -->
          <div class="rounded-lg bg-gray-1 p-6 dark:bg-dark-2">
            <h3 class="mb-4 text-xl font-semibold text-dark dark:text-white">
              🔧 API调用状态
            </h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-body-color dark:text-dark-6">新闻API</span>
                <span class="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                  ✅ 成功
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-body-color dark:text-dark-6">天气API</span>
                <span class="rounded-full bg-yellow-100 px-3 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  ⚠️ 需要配置
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-body-color dark:text-dark-6">数据条数</span>
                <span class="font-medium text-dark dark:text-white">
                  {newsData.length}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 客户端API调用示例 -->
    <section class="py-20 bg-gray-1 dark:bg-dark-2">
      <div class="container">
        <div class="mb-12 text-center">
          <h2 class="mb-4 text-3xl font-bold text-dark dark:text-white">
            客户端API调用
          </h2>
          <p class="text-body-color dark:text-dark-6">
            在浏览器中动态获取和提交数据
          </p>
        </div>

        <div class="mx-auto max-w-4xl">
          <!-- 动态数据获取 -->
          <div class="mb-8 rounded-lg bg-white p-6 shadow-md dark:bg-dark">
            <h3 class="mb-4 text-xl font-semibold text-dark dark:text-white">
              🔄 动态数据获取
            </h3>
            <div class="mb-4 flex gap-4">
              <button 
                id="fetch-users" 
                class="rounded-md bg-primary px-4 py-2 text-white hover:bg-blue-dark"
              >
                获取用户列表
              </button>
              <button 
                id="fetch-posts" 
                class="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700"
              >
                获取文章列表
              </button>
              <button 
                id="clear-data" 
                class="rounded-md bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
              >
                清空数据
              </button>
            </div>
            <div id="dynamic-content" class="min-h-[200px] rounded-md bg-gray-50 p-4 dark:bg-dark-2">
              <p class="text-center text-body-color dark:text-dark-6">
                点击上方按钮获取数据...
              </p>
            </div>
          </div>

          <!-- 数据提交表单 -->
          <div class="rounded-lg bg-white p-6 shadow-md dark:bg-dark">
            <h3 class="mb-4 text-xl font-semibold text-dark dark:text-white">
              📝 数据提交示例
            </h3>
            <form id="post-form" class="space-y-4">
              <div>
                <label class="mb-2 block text-sm font-medium text-dark dark:text-white">
                  文章标题
                </label>
                <input 
                  type="text" 
                  id="post-title" 
                  required
                  class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none dark:border-dark-3 dark:bg-dark-2 dark:text-white"
                  placeholder="请输入文章标题"
                />
              </div>
              <div>
                <label class="mb-2 block text-sm font-medium text-dark dark:text-white">
                  文章内容
                </label>
                <textarea 
                  id="post-content" 
                  required
                  rows="4"
                  class="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none dark:border-dark-3 dark:bg-dark-2 dark:text-white"
                  placeholder="请输入文章内容"
                ></textarea>
              </div>
              <button 
                type="submit" 
                class="w-full rounded-md bg-primary px-4 py-2 text-white hover:bg-blue-dark disabled:opacity-50"
              >
                提交文章
              </button>
            </form>
            <div id="form-result" class="mt-4"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 外部文章组件 -->
    <ExternalPosts />
  </main>
</Layout>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const dynamicContent = document.getElementById('dynamic-content');
    const fetchUsersBtn = document.getElementById('fetch-users');
    const fetchPostsBtn = document.getElementById('fetch-posts');
    const clearDataBtn = document.getElementById('clear-data');
    const postForm = document.getElementById('post-form');
    const formResult = document.getElementById('form-result');

    // 获取用户列表
    fetchUsersBtn?.addEventListener('click', async () => {
      fetchUsersBtn.textContent = '加载中...';
      fetchUsersBtn.disabled = true;

      try {
        const response = await fetch('https://jsonplaceholder.typicode.com/users?_limit=5');
        const users = await response.json();
        
        const usersHTML = users.map(user => `
          <div class="mb-3 rounded-md bg-white p-3 shadow-sm dark:bg-dark-3">
            <h4 class="font-medium text-dark dark:text-white">${user.name}</h4>
            <p class="text-sm text-body-color dark:text-dark-6">${user.email}</p>
            <p class="text-xs text-gray-500">${user.company.name}</p>
          </div>
        `).join('');

        dynamicContent.innerHTML = `
          <h4 class="mb-3 font-medium text-dark dark:text-white">👥 用户列表</h4>
          ${usersHTML}
        `;
      } catch (error) {
        dynamicContent.innerHTML = `
          <div class="text-red-600">获取用户列表失败: ${error.message}</div>
        `;
      } finally {
        fetchUsersBtn.textContent = '获取用户列表';
        fetchUsersBtn.disabled = false;
      }
    });

    // 获取文章列表
    fetchPostsBtn?.addEventListener('click', async () => {
      fetchPostsBtn.textContent = '加载中...';
      fetchPostsBtn.disabled = true;

      try {
        const response = await fetch('/api/posts');
        const result = await response.json();
        
        if (result.success) {
          const postsHTML = result.data.map(post => `
            <div class="mb-3 rounded-md bg-white p-3 shadow-sm dark:bg-dark-3">
              <h4 class="font-medium text-dark dark:text-white">${post.title}</h4>
              <p class="text-sm text-body-color dark:text-dark-6">${post.excerpt}</p>
              <p class="text-xs text-gray-500">作者: ${post.author}</p>
            </div>
          `).join('');

          dynamicContent.innerHTML = `
            <h4 class="mb-3 font-medium text-dark dark:text-white">📚 文章列表</h4>
            ${postsHTML}
          `;
        } else {
          throw new Error(result.error);
        }
      } catch (error) {
        dynamicContent.innerHTML = `
          <div class="text-red-600">获取文章列表失败: ${error.message}</div>
        `;
      } finally {
        fetchPostsBtn.textContent = '获取文章列表';
        fetchPostsBtn.disabled = false;
      }
    });

    // 清空数据
    clearDataBtn?.addEventListener('click', () => {
      dynamicContent.innerHTML = `
        <p class="text-center text-body-color dark:text-dark-6">
          点击上方按钮获取数据...
        </p>
      `;
    });

    // 表单提交
    postForm?.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const title = document.getElementById('post-title').value;
      const content = document.getElementById('post-content').value;
      const submitBtn = postForm.querySelector('button[type="submit"]');
      
      submitBtn.textContent = '提交中...';
      submitBtn.disabled = true;

      try {
        const response = await fetch('/api/posts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ title, content })
        });

        const result = await response.json();
        
        if (result.success) {
          formResult.innerHTML = `
            <div class="rounded-md bg-green-50 p-3 text-green-800 dark:bg-green-900 dark:text-green-200">
              ✅ ${result.message}
              <br>
              <small>文章ID: ${result.data.id}</small>
            </div>
          `;
          postForm.reset();
        } else {
          throw new Error(result.error);
        }
      } catch (error) {
        formResult.innerHTML = `
          <div class="rounded-md bg-red-50 p-3 text-red-800 dark:bg-red-900 dark:text-red-200">
            ❌ 提交失败: ${error.message}
          </div>
        `;
      } finally {
        submitBtn.textContent = '提交文章';
        submitBtn.disabled = false;
      }
    });
  });
</script>
