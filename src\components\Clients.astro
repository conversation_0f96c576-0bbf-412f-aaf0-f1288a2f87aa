---
const clients = [
  { ref: "https://tailgrids.com", image: "/assets/brands/graygrids.svg", whiteImage: "/assets/brands/graygrids-white.svg", name: "tailgrids"},
  { ref: "https://ayroui.com", image: "/assets/brands/lineicons.svg", whiteImage: "/assets/brands/lineicons-white.svg", name: "ayroui"},
  { ref: "https://uideck.com", image: "/assets/brands/uideck.svg", whiteImage: "/assets/brands/uideck-white.svg", name: "uideck"},
  { ref: "https://graygrids.com", image: "/assets/brands/ayroui.svg", whiteImage: "/assets/brands/ayroui-white.svg", name: "graygrids"},
  { ref: "https://lineicons.com", image: "/assets/brands/tailgrids.svg", whiteImage: "/assets/brands/tailgrids-white.svg", name: "lineicons"},
]
---

<!-- ====== Brands Section Start -->
<section class="pb-20 dark:bg-dark">
  <div class="container px-4">
    <div class="-mx-4 flex flex-wrap items-center justify-center gap-8 xl:gap-11">
      {
        clients.map(client => (
          <a
            href={client.ref}
            target="_blank"
            rel="nofollow noopner"
          >
            <img
              src={client.image}
              alt={client.name}
              class="dark:hidden"
            />
            <img
              src={client.whiteImage}
              alt={client.name}
              class="hidden dark:block"
            />
          </a>
        ))
      }
    </div>
  </div>
</section>
<!-- ====== Brands Section End -->